import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../service/serial_gps_service.dart';
import '../service/ocr_service.dart';
import 'connectivity_manager.dart';

/// 设备状态枚举
enum DeviceStatus {
  connected,    // 已连接
  disconnected, // 未连接
  error,        // 错误
  checking,     // 检查中
  unknown,      // 未知状态
}

/// 设备信息类
class DeviceInfo {
  final String name;
  final String description;
  final DeviceStatus status;
  final String? errorMessage;
  final DateTime lastChecked;
  final Map<String, dynamic>? additionalInfo;

  DeviceInfo({
    required this.name,
    required this.description,
    required this.status,
    this.errorMessage,
    required this.lastChecked,
    this.additionalInfo,
  });

  DeviceInfo copyWith({
    String? name,
    String? description,
    DeviceStatus? status,
    String? errorMessage,
    DateTime? lastChecked,
    Map<String, dynamic>? additionalInfo,
  }) {
    return DeviceInfo(
      name: name ?? this.name,
      description: description ?? this.description,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      lastChecked: lastChecked ?? this.lastChecked,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }
}

/// 设备状态管理器
/// 统一管理串口GPS、网络通信、相机、麦克风四个设备的连接状态
class DeviceStatusManager extends ChangeNotifier {
  static DeviceStatusManager? _instance;
  static DeviceStatusManager get instance => _instance ??= DeviceStatusManager._();
  
  DeviceStatusManager._() {
    _initializeDevices();
  }

  // 设备状态映射
  final Map<String, DeviceInfo> _deviceStatus = {};
  
  // 定时器
  Timer? _statusCheckTimer;
  
  // 检查间隔（秒）
  static const int _checkIntervalSeconds = 10;

  // 错误重试次数
  static const int _maxRetryCount = 3;

  // 设备检查重试计数
  final Map<String, int> _retryCount = {};
  
  // 是否正在监控
  bool _isMonitoring = false;

  /// 获取所有设备状态
  Map<String, DeviceInfo> get deviceStatus => Map.unmodifiable(_deviceStatus);
  
  /// 获取特定设备状态
  DeviceInfo? getDeviceStatus(String deviceKey) => _deviceStatus[deviceKey];
  
  /// 是否正在监控
  bool get isMonitoring => _isMonitoring;

  /// 初始化设备列表
  void _initializeDevices() {
    final now = DateTime.now();
    
    _deviceStatus['gps'] = DeviceInfo(
      name: '串口GPS',
      description: '高精度GPS定位模块',
      status: DeviceStatus.unknown,
      lastChecked: now,
    );
    
    _deviceStatus['network'] = DeviceInfo(
      name: '网络通信',
      description: '网络连接状态',
      status: DeviceStatus.unknown,
      lastChecked: now,
    );
    
    _deviceStatus['camera'] = DeviceInfo(
      name: '相机设备',
      description: '图像采集设备',
      status: DeviceStatus.unknown,
      lastChecked: now,
    );
    
    _deviceStatus['microphone'] = DeviceInfo(
      name: '语音设备',
      description: '音频录制设备',
      status: DeviceStatus.unknown,
      lastChecked: now,
    );
  }

  /// 开始监控设备状态
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    print('[设备状态管理器] 开始监控设备状态');
    
    // 立即检查一次
    checkAllDevices();
    
    // 启动定时检查
    _statusCheckTimer = Timer.periodic(
      Duration(seconds: _checkIntervalSeconds),
      (_) => checkAllDevices(),
    );
    
    // 启动网络连接监控
    ConnectivityManager.instance.startMonitoring();
    ConnectivityManager.instance.addListener(_onNetworkStatusChanged);
  }

  /// 停止监控设备状态
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    print('[设备状态管理器] 停止监控设备状态');
    
    _statusCheckTimer?.cancel();
    _statusCheckTimer = null;
    
    ConnectivityManager.instance.removeListener(_onNetworkStatusChanged);
    ConnectivityManager.instance.stopMonitoring();
  }

  /// 检查所有设备状态
  Future<void> checkAllDevices() async {
    if (!_isMonitoring) return;

    _logInfo('检查所有设备状态...');

    // 并行检查所有设备，但捕获每个设备的错误
    final results = await Future.wait([
      _checkDeviceWithRetry('gps', _checkGPSStatus),
      _checkDeviceWithRetry('network', _checkNetworkStatus),
      _checkDeviceWithRetry('camera', _checkCameraStatus),
      _checkDeviceWithRetry('microphone', _checkMicrophoneStatus),
    ], eagerError: false);

    // 统计检查结果
    final successCount = results.where((r) => r).length;
    _logInfo('设备状态检查完成: $successCount/${results.length} 成功');

    notifyListeners();
  }

  /// 带重试机制的设备检查
  Future<bool> _checkDeviceWithRetry(String deviceKey, Future<void> Function() checkFunction) async {
    try {
      await checkFunction();
      // 检查成功，重置重试计数
      _retryCount[deviceKey] = 0;
      return true;
    } catch (e) {
      final currentRetryCount = _retryCount[deviceKey] ?? 0;

      if (currentRetryCount < _maxRetryCount) {
        _retryCount[deviceKey] = currentRetryCount + 1;
        _logWarning('设备 $deviceKey 检查失败，将重试 (${currentRetryCount + 1}/$_maxRetryCount): $e');

        // 等待一段时间后重试
        await Future.delayed(Duration(seconds: 1));
        return await _checkDeviceWithRetry(deviceKey, checkFunction);
      } else {
        _logError('设备 $deviceKey 检查失败，已达到最大重试次数: $e');
        _updateDeviceStatus(
          deviceKey,
          DeviceStatus.error,
          errorMessage: '检查失败 (已重试$_maxRetryCount次): ${e.toString().length > 50 ? e.toString().substring(0, 50) + '...' : e.toString()}',
        );
        return false;
      }
    }
  }

  /// 检查特定设备状态
  Future<void> checkDeviceStatus(String deviceKey) async {
    switch (deviceKey) {
      case 'gps':
        await _checkGPSStatus();
        break;
      case 'network':
        await _checkNetworkStatus();
        break;
      case 'camera':
        await _checkCameraStatus();
        break;
      case 'microphone':
        await _checkMicrophoneStatus();
        break;
    }
    notifyListeners();
  }

  /// 网络状态变化回调
  void _onNetworkStatusChanged() {
    _checkNetworkStatus();
    notifyListeners();
  }

  /// 检查GPS状态
  Future<void> _checkGPSStatus() async {
    try {
      _updateDeviceStatus('gps', DeviceStatus.checking);

      final serialGPS = SerialGPSService.instance;
      bool isConnected = serialGPS.isConnected;

      // 如果未连接，尝试连接
      if (!isConnected) {
        isConnected = await serialGPS.connect();
      }

      if (isConnected) {
        // 检查是否有GPS数据
        final gpsData = serialGPS.latestData;
        final additionalInfo = <String, dynamic>{
          'port': '/dev/ttyS7',
          'baudRate': 115200,
          'hasData': gpsData != null,
        };

        if (gpsData != null) {
          additionalInfo['latitude'] = gpsData.latitude;
          additionalInfo['longitude'] = gpsData.longitude;
          additionalInfo['satelliteCount'] = gpsData.satelliteCount;
        }

        _updateDeviceStatus(
          'gps',
          DeviceStatus.connected,
          additionalInfo: additionalInfo,
        );
      } else {
        _updateDeviceStatus(
          'gps',
          DeviceStatus.disconnected,
          errorMessage: '串口连接失败',
          additionalInfo: {'port': '/dev/ttyS7', 'baudRate': 115200},
        );
      }
    } catch (e) {
      _updateDeviceStatus(
        'gps',
        DeviceStatus.error,
        errorMessage: 'GPS检查异常: $e',
      );
    }
  }

  /// 检查网络状态
  Future<void> _checkNetworkStatus() async {
    try {
      _updateDeviceStatus('network', DeviceStatus.checking);

      final connectivityManager = ConnectivityManager.instance;
      final isConnected = connectivityManager.isConnected;

      if (isConnected) {
        // 进一步测试网络连通性
        final testResult = await connectivityManager.checkConnection();

        _updateDeviceStatus(
          'network',
          testResult ? DeviceStatus.connected : DeviceStatus.disconnected,
          additionalInfo: {
            'testHost': 'www.baidu.com',
            'lastTest': DateTime.now().toIso8601String(),
          },
          errorMessage: testResult ? null : '网络连通性测试失败',
        );
      } else {
        _updateDeviceStatus(
          'network',
          DeviceStatus.disconnected,
          errorMessage: '网络未连接',
        );
      }
    } catch (e) {
      _updateDeviceStatus(
        'network',
        DeviceStatus.error,
        errorMessage: '网络检查异常: $e',
      );
    }
  }

  /// 检查相机状态
  Future<void> _checkCameraStatus() async {
    try {
      _updateDeviceStatus('camera', DeviceStatus.checking);

      // 检查可用相机列表
      try {
        await OCRService.initializeCameras();
        final cameras = OCRService.availableCamerasList;

        _updateDeviceStatus(
          'camera',
          cameras.isNotEmpty ? DeviceStatus.connected : DeviceStatus.disconnected,
          additionalInfo: {
            'cameraCount': cameras.length,
            'cameras': cameras.map((c) => {
              'name': c.name,
              'lensDirection': c.lensDirection.toString(),
            }).toList(),
          },
          errorMessage: cameras.isEmpty ? '未发现可用相机' : null,
        );
      } catch (e) {
        // 如果是权限问题，通常会包含permission相关的错误信息
        final isPermissionError = e.toString().toLowerCase().contains('permission');
        _updateDeviceStatus(
          'camera',
          DeviceStatus.error,
          errorMessage: isPermissionError ? '相机权限未授予' : '相机初始化失败: $e',
          additionalInfo: {'isPermissionError': isPermissionError},
        );
      }
    } catch (e) {
      _updateDeviceStatus(
        'camera',
        DeviceStatus.error,
        errorMessage: '相机检查异常: $e',
      );
    }
  }

  /// 检查麦克风状态
  Future<void> _checkMicrophoneStatus() async {
    try {
      _updateDeviceStatus('microphone', DeviceStatus.checking);

      // 在Android平台上，麦克风权限通常在AndroidManifest.xml中声明
      // 这里我们简单检查平台和基本可用性
      if (Platform.isAndroid || Platform.isIOS) {
        // 检查音频设备文件（Linux/Android）
        bool hasAudioDevice = false;

        if (Platform.isLinux || Platform.isAndroid) {
          try {
            // 检查常见的音频设备文件
            final audioDevices = [
              '/dev/snd/',
              '/proc/asound/',
            ];

            for (final devicePath in audioDevices) {
              final dir = Directory(devicePath);
              if (await dir.exists()) {
                hasAudioDevice = true;
                break;
              }
            }
          } catch (e) {
            print('[麦克风检查] 音频设备检查失败: $e');
          }
        } else {
          // iOS/其他平台假设有音频设备
          hasAudioDevice = true;
        }

        _updateDeviceStatus(
          'microphone',
          hasAudioDevice ? DeviceStatus.connected : DeviceStatus.disconnected,
          additionalInfo: {
            'platform': Platform.operatingSystem,
            'hasAudioDevice': hasAudioDevice,
            'note': '权限需要在运行时检查',
          },
          errorMessage: hasAudioDevice ? null : '未检测到音频设备',
        );
      } else {
        _updateDeviceStatus(
          'microphone',
          DeviceStatus.unknown,
          errorMessage: '不支持的平台',
          additionalInfo: {'platform': Platform.operatingSystem},
        );
      }
    } catch (e) {
      _updateDeviceStatus(
        'microphone',
        DeviceStatus.error,
        errorMessage: '麦克风检查异常: $e',
      );
    }
  }

  /// 更新设备状态
  void _updateDeviceStatus(
    String deviceKey,
    DeviceStatus status, {
    String? errorMessage,
    Map<String, dynamic>? additionalInfo,
  }) {
    final currentDevice = _deviceStatus[deviceKey];
    if (currentDevice == null) return;

    _deviceStatus[deviceKey] = currentDevice.copyWith(
      status: status,
      errorMessage: errorMessage,
      lastChecked: DateTime.now(),
      additionalInfo: additionalInfo,
    );
  }

  /// 日志记录方法
  void _logInfo(String message) {
    if (kDebugMode) {
      debugPrint('[设备状态管理器] ℹ️ $message');
    }
  }

  void _logWarning(String message) {
    if (kDebugMode) {
      debugPrint('[设备状态管理器] ⚠️ $message');
    }
  }

  void _logError(String message) {
    if (kDebugMode) {
      debugPrint('[设备状态管理器] ❌ $message');
    }
  }

  /// 获取设备状态统计信息
  Map<String, int> getStatusStatistics() {
    final stats = <String, int>{
      'connected': 0,
      'disconnected': 0,
      'error': 0,
      'checking': 0,
      'unknown': 0,
    };

    for (final device in _deviceStatus.values) {
      switch (device.status) {
        case DeviceStatus.connected:
          stats['connected'] = (stats['connected'] ?? 0) + 1;
          break;
        case DeviceStatus.disconnected:
          stats['disconnected'] = (stats['disconnected'] ?? 0) + 1;
          break;
        case DeviceStatus.error:
          stats['error'] = (stats['error'] ?? 0) + 1;
          break;
        case DeviceStatus.checking:
          stats['checking'] = (stats['checking'] ?? 0) + 1;
          break;
        case DeviceStatus.unknown:
          stats['unknown'] = (stats['unknown'] ?? 0) + 1;
          break;
      }
    }

    return stats;
  }

  /// 获取系统整体健康状态
  String getSystemHealthStatus() {
    final stats = getStatusStatistics();
    final totalDevices = _deviceStatus.length;
    final connectedDevices = stats['connected'] ?? 0;
    final errorDevices = stats['error'] ?? 0;

    if (errorDevices > 0) {
      return '系统异常 ($errorDevices个设备错误)';
    } else if (connectedDevices == totalDevices) {
      return '系统正常 (所有设备已连接)';
    } else if (connectedDevices > totalDevices / 2) {
      return '系统基本正常 ($connectedDevices/$totalDevices设备已连接)';
    } else {
      return '系统状态不佳 ($connectedDevices/$totalDevices设备已连接)';
    }
  }

  @override
  void dispose() {
    stopMonitoring();
    super.dispose();
  }
}
