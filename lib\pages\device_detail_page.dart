import 'package:flutter/material.dart';
import '../theme/industrial_theme.dart';
import '../widgets/device_status_widget.dart';
import '../utils/device_status_manager.dart';

/// 设备详细状态页面
/// 显示所有设备的详细连接状态和运行信息
class DeviceDetailPage extends StatefulWidget {
  const DeviceDetailPage({Key? key}) : super(key: key);

  @override
  State<DeviceDetailPage> createState() => _DeviceDetailPageState();
}

class _DeviceDetailPageState extends State<DeviceDetailPage> {
  late DeviceStatusManager _statusManager;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _statusManager = DeviceStatusManager.instance;
    _statusManager.addListener(_onStatusChanged);
    
    // 确保监控已启动
    if (!_statusManager.isMonitoring) {
      _statusManager.startMonitoring();
    }
  }

  @override
  void dispose() {
    _statusManager.removeListener(_onStatusChanged);
    super.dispose();
  }

  void _onStatusChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _refreshDeviceStatus() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _statusManager.checkAllDevices();
      
      // 显示刷新成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text('设备状态已刷新'),
              ],
            ),
            backgroundColor: IndustrialTheme.successGreen,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text('刷新失败: $e')),
              ],
            ),
            backgroundColor: IndustrialTheme.errorRed,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final systemHealth = _statusManager.getSystemHealthStatus();
    final stats = _statusManager.getStatusStatistics();
    
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '设备运行状态',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: IndustrialTheme.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: _isRefreshing 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isRefreshing ? null : _refreshDeviceStatus,
            tooltip: '刷新设备状态',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              IndustrialTheme.surfaceGrey.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            // 系统概览卡片
            // Container(
            //   margin: const EdgeInsets.all(16),
            //   padding: const EdgeInsets.all(20),
            //   decoration: BoxDecoration(
            //     gradient: LinearGradient(
            //       begin: Alignment.topLeft,
            //       end: Alignment.bottomRight,
            //       colors: [
            //         IndustrialTheme.primaryBlue,
            //         IndustrialTheme.primaryBlue.withValues(alpha: 0.8),
            //       ],
            //     ),
            //     borderRadius: BorderRadius.circular(16),
            //     boxShadow: [
            //       BoxShadow(
            //         color: IndustrialTheme.primaryBlue.withValues(alpha: 0.3),
            //         blurRadius: 12,
            //         offset: const Offset(0, 4),
            //       ),
            //     ],
            //   ),
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.start,
            //     children: [
            //       Row(
            //         children: [
            //           // Container(
            //           //   padding: const EdgeInsets.all(12),
            //           //   decoration: BoxDecoration(
            //           //     color: Colors.white.withValues(alpha: 0.2),
            //           //     borderRadius: BorderRadius.circular(12),
            //           //   ),
            //           //   child: const Icon(
            //           //     Icons.dashboard,
            //           //     color: Colors.white,
            //           //     size: 28,
            //           //   ),
            //           // ),
            //           // const SizedBox(width: 16),
            //           Expanded(
            //             child: Column(
            //               crossAxisAlignment: CrossAxisAlignment.start,
            //               children: [
            //                 const Text(
            //                   '系统运行状态',
            //                   style: TextStyle(
            //                     color: Colors.white,
            //                     fontSize: 20,
            //                     fontWeight: FontWeight.bold,
            //                   ),
            //                 ),
            //                 const SizedBox(height: 4),
            //                 Text(
            //                   systemHealth,
            //                   style: const TextStyle(
            //                     color: Colors.white70,
            //                     fontSize: 16,
            //                   ),
            //                 ),
            //               ],
            //             ),
            //           ),
            //         ],
            //       ),
            //       const SizedBox(height: 20),
            //       Row(
            //         children: [
            //           _buildStatItem('已连接', stats['connected'] ?? 0, IndustrialTheme.successGreen),
            //           _buildStatItem('未连接', stats['disconnected'] ?? 0, IndustrialTheme.warningAmber),
            //           _buildStatItem('错误', stats['error'] ?? 0, IndustrialTheme.errorRed),
            //           _buildStatItem('检查中', stats['checking'] ?? 0, IndustrialTheme.processingPurple),
            //         ],
            //       ),
            //     ],
            //   ),
            // ),
            
            // 设备详细状态列表
            Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: DeviceStatusWidget(
                    showDetails: true,
                    onRefresh: _refreshDeviceStatus,
                  ),
                ),
              ),
            ),
            
            // 底部操作按钮
            // Container(
            //   padding: const EdgeInsets.all(16),
            //   decoration: BoxDecoration(
            //     color: Colors.white,
            //     boxShadow: [
            //       BoxShadow(
            //         color: Colors.black.withValues(alpha: 0.1),
            //         blurRadius: 8,
            //         offset: const Offset(0, -2),
            //       ),
            //     ],
            //   ),
            //   child: Row(
            //     children: [
            //       Expanded(
            //         child: ElevatedButton.icon(
            //           onPressed: () => Navigator.of(context).pop(),
            //           icon: const Icon(Icons.home),
            //           label: const Text('返回主页'),
            //           style: ElevatedButton.styleFrom(
            //             backgroundColor: IndustrialTheme.primaryBlue,
            //             foregroundColor: Colors.white,
            //             padding: const EdgeInsets.symmetric(vertical: 16),
            //             shape: RoundedRectangleBorder(
            //               borderRadius: BorderRadius.circular(12),
            //             ),
            //           ),
            //         ),
            //       ),
            //       const SizedBox(width: 12),
            //       Expanded(
            //         child: ElevatedButton.icon(
            //           onPressed: _isRefreshing ? null : _refreshDeviceStatus,
            //           icon: _isRefreshing 
            //               ? const SizedBox(
            //                   width: 16,
            //                   height: 16,
            //                   child: CircularProgressIndicator(
            //                     strokeWidth: 2,
            //                     valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            //                   ),
            //                 )
            //               : const Icon(Icons.refresh),
            //           label: Text(_isRefreshing ? '刷新中...' : '刷新状态'),
            //           style: ElevatedButton.styleFrom(
            //             backgroundColor: IndustrialTheme.successGreen,
            //             foregroundColor: Colors.white,
            //             padding: const EdgeInsets.symmetric(vertical: 16),
            //             shape: RoundedRectangleBorder(
            //               borderRadius: BorderRadius.circular(12),
            //             ),
            //           ),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项目
  Widget _buildStatItem(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            Text(
              count.toString(),
              style: TextStyle(
                color: color,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
